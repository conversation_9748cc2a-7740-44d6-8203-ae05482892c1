// 用户类型
export interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// 小说类型
export interface Novel {
  id: number
  title: string
  description?: string
  cover_image?: string
  status: 'draft' | 'published' | 'completed'
  word_count: number
  user_id: number
  created_at: string
  updated_at: string
}

// 包含章节的小说类型
export interface NovelWithChapters extends Novel {
  chapters: Chapter[]
}

// 章节类型
export interface Chapter {
  id: number
  title: string
  content: string
  chapter_number: number
  word_count: number
  novel_id: number
  created_at: string
  updated_at: string
}

// 认证相关类型
export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface TokenResponse {
  access_token: string
  token_type: string
}

// AI 功能相关类型
export interface AIRequest {
  text: string
  operation_type: 'refine' | 'expand' | 'continue' | 'summarize'
  chapter_id: number
}

export interface AIResponse {
  generated_text: string
  operation_type: string
  original_text: string
}

export interface AIOperation {
  id: number
  operation_type: string
  original_text: string
  generated_text: string
  prompt_used?: string
  user_id: number
  chapter_id: number
  created_at: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
}

// 文件导入类型
export interface ImportFileRequest {
  novel_id: number
  file: File
}
