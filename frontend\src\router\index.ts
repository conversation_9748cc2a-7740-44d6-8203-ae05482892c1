import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/HomeView.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/editor',
    name: 'Editor',
    component: () => import('../views/EditorView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
