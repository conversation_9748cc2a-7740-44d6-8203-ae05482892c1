/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 暗色主题变量 */
:root {
  --ink-bg-primary: #1a1a1a;
  --ink-bg-secondary: #2d2d2d;
  --ink-bg-tertiary: #3a3a3a;
  --ink-text-primary: #ffffff;
  --ink-text-secondary: #cccccc;
  --ink-text-muted: #999999;
  --ink-border: #404040;
  --ink-accent: #409eff;
  --ink-accent-hover: #66b1ff;
  --ink-success: #67c23a;
  --ink-warning: #e6a23c;
  --ink-danger: #f56c6c;
}

/* 暗色主题样式 */
.dark-theme {
  background-color: var(--ink-bg-primary);
  color: var(--ink-text-primary);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ink-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--ink-bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 编辑器样式 */
.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 12px 16px;
  background: var(--ink-bg-secondary);
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  gap: 12px;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  background: var(--ink-bg-secondary);
  border-right: 1px solid var(--ink-border);
  height: 100%;
  overflow-y: auto;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 小说列表样式 */
.novel-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--ink-border);
  transition: background-color 0.2s;
}

.novel-item:hover {
  background: var(--ink-bg-tertiary);
}

.novel-item.active {
  background: var(--ink-accent);
  color: white;
}

.chapter-item {
  padding: 8px 32px;
  cursor: pointer;
  font-size: 14px;
  color: var(--ink-text-secondary);
  transition: all 0.2s;
}

.chapter-item:hover {
  background: var(--ink-bg-tertiary);
  color: var(--ink-text-primary);
}

.chapter-item.active {
  background: var(--ink-accent);
  color: white;
}

/* 工具栏按钮样式 */
.toolbar-btn {
  padding: 8px 16px;
  border: 1px solid var(--ink-border);
  background: var(--ink-bg-tertiary);
  color: var(--ink-text-primary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.toolbar-btn:hover {
  background: var(--ink-accent);
  border-color: var(--ink-accent);
}

.toolbar-btn.primary {
  background: var(--ink-accent);
  border-color: var(--ink-accent);
  color: white;
}

.toolbar-btn.primary:hover {
  background: var(--ink-accent-hover);
  border-color: var(--ink-accent-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }
  
  .sidebar {
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--ink-border);
  }
}
