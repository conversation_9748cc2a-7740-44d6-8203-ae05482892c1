<template>
  <div class="editor-view">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <el-button 
          text 
          @click="goHome"
          class="home-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
      </div>
      
      <div class="navbar-center">
        <h1 class="app-title">妙笔（InkMind）</h1>
      </div>
      
      <div class="navbar-right">
        <el-dropdown @command="handleUserCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            {{ authStore.user?.username }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="settings">设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-layout">
      <!-- 左侧小说列表 -->
      <NovelList />
      
      <!-- 右侧编辑器 -->
      <div class="editor-section">
        <Editor />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isInitializing" class="loading-overlay">
      <el-loading-spinner size="large" />
      <p>正在初始化编辑器...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, User, ArrowDown } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useNovelStore } from '@/stores/novel'
import NovelList from '@/components/NovelList.vue'
import Editor from '@/components/Editor.vue'

const router = useRouter()
const authStore = useAuthStore()
const novelStore = useNovelStore()

const isInitializing = ref(true)

// 返回首页
const goHome = () => {
  router.push('/')
}

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

// 初始化编辑器
const initializeEditor = async () => {
  try {
    // 检查登录状态
    if (!authStore.isAuthenticated) {
      router.push('/login')
      return
    }

    // 获取小说列表
    await novelStore.fetchNovels()
    
    // 如果有小说，自动选择第一个
    if (novelStore.novels.length > 0) {
      await novelStore.selectNovel(novelStore.novels[0])
      
      // 如果有章节，自动选择第一个
      if (novelStore.currentNovelChapters.length > 0) {
        await novelStore.selectChapter(novelStore.currentNovelChapters[0])
      }
    }
    
  } catch (error) {
    console.error('初始化编辑器失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  } finally {
    isInitializing.value = false
  }
}

// 组件挂载
onMounted(() => {
  initializeEditor()
})
</script>

<style scoped>
.editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--ink-bg-primary);
}

.top-navbar {
  height: 60px;
  background: var(--ink-bg-secondary);
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  flex-shrink: 0;
}

.navbar-left {
  flex: 1;
}

.home-btn {
  color: var(--ink-text-secondary);
  font-size: 14px;
}

.home-btn:hover {
  color: var(--ink-accent);
}

.navbar-center {
  flex: 1;
  text-align: center;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ink-text-primary);
}

.navbar-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: var(--ink-text-primary);
  font-size: 14px;
  transition: all 0.2s;
}

.user-info:hover {
  background: var(--ink-bg-tertiary);
  color: var(--ink-accent);
}

.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-section {
  flex: 1;
  overflow: hidden;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  color: var(--ink-text-primary);
}

.loading-overlay p {
  margin-top: 16px;
  font-size: 14px;
  color: var(--ink-text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navbar {
    padding: 0 12px;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .main-layout {
    flex-direction: column;
  }
  
  .navbar-left,
  .navbar-right {
    flex: 0;
  }
  
  .navbar-center {
    flex: 1;
  }
}
</style>
