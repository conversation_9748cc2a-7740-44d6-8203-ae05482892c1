<template>
  <div class="editor-view">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <el-button 
          text 
          @click="goHome"
          class="home-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回首页
        </el-button>
      </div>
      
      <div class="navbar-center">
        <h1 class="app-title">妙笔（InkMind）</h1>
      </div>
      
      <div class="navbar-right">
        <el-button type="primary">
          <el-icon><User /></el-icon>
          用户中心
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-layout">
      <!-- 左侧小说列表 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h3>我的小说</h3>
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            新建
          </el-button>
        </div>

        <div class="novel-list">
          <div class="novel-item active">
            <div class="novel-info">
              <h4 class="novel-title">示例小说</h4>
              <p class="novel-meta">1000 字 · 3 章</p>
            </div>
          </div>

          <div class="chapter-list">
            <div class="chapter-item active">
              <span class="chapter-number">1.</span>
              <span class="chapter-title">第一章 开始</span>
              <span class="chapter-words">300字</span>
            </div>
            <div class="chapter-item">
              <span class="chapter-number">2.</span>
              <span class="chapter-title">第二章 发展</span>
              <span class="chapter-words">400字</span>
            </div>
            <div class="chapter-item">
              <span class="chapter-number">3.</span>
              <span class="chapter-title">第三章 高潮</span>
              <span class="chapter-words">300字</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧编辑器 -->
      <div class="editor-section">
        <div class="editor-container">
          <!-- 编辑器工具栏 -->
          <div class="editor-toolbar">
            <div class="toolbar-left">
              <span class="chapter-info">第一章 开始 - 300 字</span>
            </div>

            <div class="toolbar-right">
              <el-button size="small" type="primary">保存</el-button>
              <el-divider direction="vertical" />

              <!-- AI 功能按钮 -->
              <el-dropdown>
                <el-button size="small">
                  <el-icon><Magic /></el-icon>
                  AI 助手
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>润色文本</el-dropdown-item>
                    <el-dropdown-item>扩写内容</el-dropdown-item>
                    <el-dropdown-item>章节续写</el-dropdown-item>
                    <el-dropdown-item>内容总结</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 编辑器内容区域 -->
          <div class="editor-content">
            <el-input
              v-model="editorContent"
              type="textarea"
              :rows="20"
              placeholder="开始您的创作..."
              class="editor-textarea"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, User, Plus, Magic, ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const editorContent = ref(`这是一个示例章节的内容。

在这里，您可以开始您的创作。编辑器支持：

1. 实时保存
2. AI 智能润色
3. 内容扩写
4. 章节续写
5. 内容总结

选中文本后，可以使用 AI 助手功能来优化您的文字。`)

// 返回首页
const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.editor-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--ink-bg-primary);
}

.top-navbar {
  height: 60px;
  background: var(--ink-bg-secondary);
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  flex-shrink: 0;
}

.navbar-left {
  flex: 1;
}

.home-btn {
  color: var(--ink-text-secondary);
  font-size: 14px;
}

.home-btn:hover {
  color: var(--ink-accent);
}

.navbar-center {
  flex: 1;
  text-align: center;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ink-text-primary);
}

.navbar-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: var(--ink-text-primary);
  font-size: 14px;
  transition: all 0.2s;
}

.user-info:hover {
  background: var(--ink-bg-tertiary);
  color: var(--ink-accent);
}

.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: var(--ink-bg-secondary);
  border-right: 1px solid var(--ink-border);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h3 {
  margin: 0;
  color: var(--ink-text-primary);
  font-size: 16px;
}

.novel-list {
  flex: 1;
  overflow-y: auto;
}

.novel-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--ink-border);
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.novel-item:hover {
  background: var(--ink-bg-tertiary);
}

.novel-item.active {
  background: var(--ink-accent);
  color: white;
}

.novel-info {
  flex: 1;
}

.novel-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
}

.novel-meta {
  margin: 0;
  font-size: 12px;
  color: var(--ink-text-muted);
}

.novel-item.active .novel-meta {
  color: rgba(255, 255, 255, 0.8);
}

.chapter-list {
  background: var(--ink-bg-primary);
}

.chapter-item {
  padding: 8px 32px;
  cursor: pointer;
  font-size: 13px;
  color: var(--ink-text-secondary);
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chapter-item:hover {
  background: var(--ink-bg-tertiary);
  color: var(--ink-text-primary);
}

.chapter-item.active {
  background: var(--ink-accent);
  color: white;
}

.chapter-number {
  font-weight: 500;
  min-width: 20px;
}

.chapter-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chapter-words {
  font-size: 11px;
  opacity: 0.7;
}

.editor-section {
  flex: 1;
  overflow: hidden;
}

.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 12px 16px;
  background: var(--ink-bg-secondary);
  border-bottom: 1px solid var(--ink-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.chapter-info {
  color: var(--ink-text-primary);
  font-size: 14px;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.editor-textarea {
  height: 100%;
}

:deep(.editor-textarea .el-textarea__inner) {
  height: 100% !important;
  background: var(--ink-bg-primary);
  border: 1px solid var(--ink-border);
  color: var(--ink-text-primary);
  font-size: 16px;
  line-height: 1.8;
  resize: none;
}

:deep(.editor-textarea .el-textarea__inner:focus) {
  border-color: var(--ink-accent);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navbar {
    padding: 0 12px;
  }

  .app-title {
    font-size: 16px;
  }

  .main-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--ink-border);
  }

  .navbar-left,
  .navbar-right {
    flex: 0;
  }

  .navbar-center {
    flex: 1;
  }
}
</style>
