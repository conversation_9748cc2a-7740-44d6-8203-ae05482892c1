"""
小说管理相关路由
包含小说的创建、读取、更新、删除等端点
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
import chardet

from ..database import get_db
from .. import crud, schemas, models
from ..auth import get_current_active_user

router = APIRouter(prefix="/api/novels", tags=["小说管理"])


@router.get("/", response_model=List[schemas.Novel])
def read_novels(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户的小说列表"""
    novels = crud.get_novels(db, user_id=current_user.id, skip=skip, limit=limit)
    return novels


@router.post("/", response_model=schemas.Novel)
def create_novel(
    novel: schemas.NovelCreate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建新小说"""
    return crud.create_novel(db=db, novel=novel, user_id=current_user.id)


@router.get("/{novel_id}", response_model=schemas.NovelWithChapters)
def read_novel(
    novel_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取小说详情（包含章节列表）"""
    db_novel = crud.get_novel(db, novel_id=novel_id, user_id=current_user.id)
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说不存在")
    
    # 获取章节列表
    chapters = crud.get_chapters(db, novel_id=novel_id, user_id=current_user.id)
    
    # 构造响应
    novel_dict = schemas.Novel.from_orm(db_novel).dict()
    novel_dict["chapters"] = [schemas.Chapter.from_orm(chapter) for chapter in chapters]
    
    return novel_dict


@router.put("/{novel_id}", response_model=schemas.Novel)
def update_novel(
    novel_id: int,
    novel_update: schemas.NovelUpdate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新小说信息"""
    db_novel = crud.update_novel(
        db, novel_id=novel_id, user_id=current_user.id, novel_update=novel_update
    )
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说不存在")
    return db_novel


@router.delete("/{novel_id}", response_model=schemas.MessageResponse)
def delete_novel(
    novel_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除小说"""
    success = crud.delete_novel(db, novel_id=novel_id, user_id=current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="小说不存在")
    return {"message": "小说删除成功", "success": True}


@router.post("/{novel_id}/import", response_model=schemas.MessageResponse)
async def import_novel_file(
    novel_id: int,
    file: UploadFile = File(...),
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """导入小说文件（支持 .txt, .md 格式）"""
    # 验证小说是否存在且属于当前用户
    db_novel = crud.get_novel(db, novel_id=novel_id, user_id=current_user.id)
    if db_novel is None:
        raise HTTPException(status_code=404, detail="小说不存在")
    
    # 验证文件格式
    allowed_extensions = [".txt", ".md"]
    file_extension = "." + file.filename.split(".")[-1].lower()
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式。支持的格式：{', '.join(allowed_extensions)}"
        )
    
    try:
        # 读取文件内容
        content_bytes = await file.read()
        
        # 检测编码
        detected = chardet.detect(content_bytes)
        encoding = detected.get('encoding', 'utf-8')
        
        # 解码内容
        content = content_bytes.decode(encoding)
        
        # 简单的章节分割逻辑（按空行分割）
        chapters_content = content.split('\n\n')
        chapters_content = [ch.strip() for ch in chapters_content if ch.strip()]
        
        # 创建章节
        for i, chapter_content in enumerate(chapters_content, 1):
            # 提取章节标题（取第一行作为标题，如果第一行很短的话）
            lines = chapter_content.split('\n')
            if len(lines) > 1 and len(lines[0]) < 50:
                title = lines[0].strip()
                content_text = '\n'.join(lines[1:]).strip()
            else:
                title = f"第{i}章"
                content_text = chapter_content
            
            chapter_data = schemas.ChapterCreate(
                title=title,
                content=content_text,
                chapter_number=i
            )
            
            crud.create_chapter(
                db=db,
                chapter=chapter_data,
                novel_id=novel_id,
                user_id=current_user.id
            )
        
        return {
            "message": f"成功导入 {len(chapters_content)} 个章节",
            "success": True
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"文件导入失败：{str(e)}"
        )
