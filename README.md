# 妙笔（InkMind）AI 辅助小说写作平台

## 项目简介

妙笔（InkMind）是一个基于 AI 的小说写作辅助平台，提供智能润色、扩写、续写等功能，帮助作者提升写作效率和质量。

## 功能特性

- 📚 **小说管理**：创建、编辑、删除小说项目
- 📝 **章节编辑**：支持章节的增删改查
- 🤖 **AI 辅助写作**：
  - 文本润色
  - 内容扩写
  - 章节续写
  - 内容总结
- 📁 **文件导入**：支持 .txt 和 .md 格式文件导入
- 👤 **用户系统**：注册、登录、身份验证

## 技术栈

### 后端
- **FastAPI**: 现代、快速的 Web 框架
- **SQLAlchemy**: ORM 数据库操作
- **SQLite**: 轻量级数据库
- **JWT**: 用户身份验证
- **Pydantic**: 数据验证和序列化

## 快速开始

### 1. 环境要求

- Python 3.8+
- pip

### 2. 安装依赖

```bash
# 安装 Python 依赖
pip install -r requirements.txt
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，配置必要的环境变量
```

### 4. 启动服务

```bash
# 方式一：使用启动脚本
python run_server.py

# 方式二：直接使用 uvicorn
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload
```

### 5. 访问应用

- API 文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

## API 文档

启动服务后，访问 http://localhost:8000/docs 查看完整的 API 文档。

### 主要端点

#### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

#### 小说管理
- `GET /api/novels/` - 获取小说列表
- `POST /api/novels/` - 创建小说
- `GET /api/novels/{novel_id}` - 获取小说详情
- `PUT /api/novels/{novel_id}` - 更新小说
- `DELETE /api/novels/{novel_id}` - 删除小说
- `POST /api/novels/{novel_id}/import` - 导入文件

#### 章节管理
- `GET /api/chapters/{chapter_id}` - 获取章节
- `POST /api/chapters/` - 创建章节
- `PUT /api/chapters/{chapter_id}` - 更新章节
- `DELETE /api/chapters/{chapter_id}` - 删除章节

#### AI 功能
- `POST /api/chapters/ai/refine` - 文本润色
- `POST /api/chapters/ai/expand` - 文本扩写
- `POST /api/chapters/ai/continue` - 章节续写
- `POST /api/chapters/ai/summarize` - 内容总结
- `GET /api/chapters/{chapter_id}/ai-history` - AI 操作历史

## 项目结构

```
ink-mind/
├── backend/                    # 后端代码
│   ├── __init__.py
│   ├── main.py                # FastAPI 应用入口
│   ├── config.py              # 配置文件
│   ├── database.py            # 数据库连接
│   ├── models.py              # 数据库模型
│   ├── schemas.py             # Pydantic 模型
│   ├── crud.py                # CRUD 操作
│   ├── auth.py                # 认证相关
│   └── routers/               # API 路由
│       ├── __init__.py
│       ├── auth.py            # 认证路由
│       ├── novels.py          # 小说管理路由
│       └── chapters.py        # 章节和 AI 功能路由
├── requirements.txt           # Python 依赖
├── run_server.py             # 服务器启动脚本
├── .env.example              # 环境变量模板
└── README.md                 # 项目说明
```

## 开发说明

### AI 功能集成

当前 AI 功能使用占位符实现，位于 `backend/routers/chapters.py` 中的 `mock_ai_service` 函数。

要集成真实的 AI API，需要：

1. 在 `config.py` 中配置 AI API 的 URL 和密钥
2. 替换 `mock_ai_service` 函数，实现真实的 API 调用
3. 处理 API 响应和错误情况

### 数据库

项目使用 SQLite 作为默认数据库，数据库文件会自动创建在项目根目录下的 `ink_mind.db`。

如需使用其他数据库，修改 `.env` 文件中的 `DATABASE_URL` 配置。

## 下一步开发

1. 前端界面开发（Vue 3 + TypeScript）
2. AI API 集成
3. 用户界面优化
4. 部署配置

## 许可证

MIT License
