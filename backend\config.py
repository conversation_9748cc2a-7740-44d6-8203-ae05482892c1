"""
配置文件
包含数据库连接、JWT密钥等配置信息
"""
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用配置类"""
    
    # 数据库配置
    database_url: str = "sqlite:///./ink_mind.db"
    
    # JWT 配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI API 配置 (占位符)
    ai_api_url: Optional[str] = None
    ai_api_key: Optional[str] = None
    
    # 应用配置
    app_name: str = "InkMind"
    debug: bool = True
    
    class Config:
        env_file = ".env"


# 全局配置实例
settings = Settings()
