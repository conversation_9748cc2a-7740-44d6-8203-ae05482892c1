"""
CRUD 操作函数
包含创建、读取、更新、删除数据库记录的函数
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from passlib.context import CryptContext

from . import models, schemas

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希值"""
    return pwd_context.hash(password)


# 用户 CRUD 操作
def get_user(db: Session, user_id: int) -> Optional[models.User]:
    """根据 ID 获取用户"""
    return db.query(models.User).filter(models.User.id == user_id).first()


def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    """根据用户名获取用户"""
    return db.query(models.User).filter(models.User.username == username).first()


def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """根据邮箱获取用户"""
    return db.query(models.User).filter(models.User.email == email).first()


def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """创建用户"""
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        password_hash=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, username: str, password: str) -> Optional[models.User]:
    """验证用户身份"""
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.password_hash):
        return None
    return user


# 小说 CRUD 操作
def get_novels(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[models.Novel]:
    """获取用户的小说列表"""
    return db.query(models.Novel).filter(
        models.Novel.user_id == user_id
    ).offset(skip).limit(limit).all()


def get_novel(db: Session, novel_id: int, user_id: int) -> Optional[models.Novel]:
    """获取指定小说"""
    return db.query(models.Novel).filter(
        and_(models.Novel.id == novel_id, models.Novel.user_id == user_id)
    ).first()


def create_novel(db: Session, novel: schemas.NovelCreate, user_id: int) -> models.Novel:
    """创建小说"""
    db_novel = models.Novel(**novel.dict(), user_id=user_id)
    db.add(db_novel)
    db.commit()
    db.refresh(db_novel)
    return db_novel


def update_novel(db: Session, novel_id: int, user_id: int, novel_update: schemas.NovelUpdate) -> Optional[models.Novel]:
    """更新小说"""
    db_novel = get_novel(db, novel_id, user_id)
    if not db_novel:
        return None
    
    update_data = novel_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_novel, field, value)
    
    db.commit()
    db.refresh(db_novel)
    return db_novel


def delete_novel(db: Session, novel_id: int, user_id: int) -> bool:
    """删除小说"""
    db_novel = get_novel(db, novel_id, user_id)
    if not db_novel:
        return False
    
    db.delete(db_novel)
    db.commit()
    return True


# 章节 CRUD 操作
def get_chapters(db: Session, novel_id: int, user_id: int) -> List[models.Chapter]:
    """获取小说的章节列表"""
    return db.query(models.Chapter).join(models.Novel).filter(
        and_(models.Novel.id == novel_id, models.Novel.user_id == user_id)
    ).order_by(models.Chapter.chapter_number).all()


def get_chapter(db: Session, chapter_id: int, user_id: int) -> Optional[models.Chapter]:
    """获取指定章节"""
    return db.query(models.Chapter).join(models.Novel).filter(
        and_(models.Chapter.id == chapter_id, models.Novel.user_id == user_id)
    ).first()


def create_chapter(db: Session, chapter: schemas.ChapterCreate, novel_id: int, user_id: int) -> Optional[models.Chapter]:
    """创建章节"""
    # 验证小说是否属于用户
    novel = get_novel(db, novel_id, user_id)
    if not novel:
        return None
    
    # 计算字数
    word_count = len(chapter.content)
    
    db_chapter = models.Chapter(
        **chapter.dict(),
        novel_id=novel_id,
        word_count=word_count
    )
    db.add(db_chapter)
    
    # 更新小说总字数
    novel.word_count = sum(ch.word_count for ch in novel.chapters) + word_count
    
    db.commit()
    db.refresh(db_chapter)
    return db_chapter


def update_chapter(db: Session, chapter_id: int, user_id: int, chapter_update: schemas.ChapterUpdate) -> Optional[models.Chapter]:
    """更新章节"""
    db_chapter = get_chapter(db, chapter_id, user_id)
    if not db_chapter:
        return None
    
    update_data = chapter_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_chapter, field, value)
    
    # 重新计算字数
    if 'content' in update_data:
        db_chapter.word_count = len(db_chapter.content)
        # 更新小说总字数
        novel = db_chapter.novel
        novel.word_count = sum(ch.word_count for ch in novel.chapters)
    
    db.commit()
    db.refresh(db_chapter)
    return db_chapter


def delete_chapter(db: Session, chapter_id: int, user_id: int) -> bool:
    """删除章节"""
    db_chapter = get_chapter(db, chapter_id, user_id)
    if not db_chapter:
        return False
    
    novel = db_chapter.novel
    db.delete(db_chapter)
    
    # 更新小说总字数
    novel.word_count = sum(ch.word_count for ch in novel.chapters if ch.id != chapter_id)
    
    db.commit()
    return True


# AI 操作 CRUD
def create_ai_operation(
    db: Session,
    operation_type: str,
    original_text: str,
    generated_text: str,
    chapter_id: int,
    user_id: int,
    prompt_used: Optional[str] = None
) -> models.AIOperation:
    """创建 AI 操作记录"""
    db_operation = models.AIOperation(
        operation_type=operation_type,
        original_text=original_text,
        generated_text=generated_text,
        prompt_used=prompt_used,
        chapter_id=chapter_id,
        user_id=user_id
    )
    db.add(db_operation)
    db.commit()
    db.refresh(db_operation)
    return db_operation


def get_ai_operations(db: Session, chapter_id: int, user_id: int) -> List[models.AIOperation]:
    """获取章节的 AI 操作历史"""
    return db.query(models.AIOperation).join(models.Chapter).join(models.Novel).filter(
        and_(
            models.AIOperation.chapter_id == chapter_id,
            models.Novel.user_id == user_id
        )
    ).order_by(models.AIOperation.created_at.desc()).all()
