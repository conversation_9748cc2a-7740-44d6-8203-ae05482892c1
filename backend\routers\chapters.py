"""
章节管理和 AI 功能相关路由
包含章节的 CRUD 操作以及 AI 辅助写作功能
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import asyncio
import random

from ..database import get_db
from .. import crud, schemas, models
from ..auth import get_current_active_user

router = APIRouter(prefix="/api/chapters", tags=["章节管理"])


# AI 功能占位符函数
async def mock_ai_service(text: str, operation_type: str) -> str:
    """
    AI 服务占位符函数
    
    TODO: 替换为真实的 AI API 调用
    这里需要集成外部大模型 API，例如：
    - 发送 HTTP 请求到 AI API 端点
    - 处理 API 响应
    - 错误处理和重试逻辑
    
    Args:
        text: 输入文本
        operation_type: 操作类型 (refine, expand, continue, summarize)
    
    Returns:
        生成的文本
    """
    # 模拟 API 调用延迟
    await asyncio.sleep(1)
    
    # 根据操作类型返回不同的模拟结果
    if operation_type == "refine":
        return f"[AI 润色结果] 经过润色的文本：{text[:50]}..."
    elif operation_type == "expand":
        return f"[AI 扩写结果] 扩写内容：{text[:30]}... 这里是扩展的详细描述和更多细节。"
    elif operation_type == "continue":
        return f"[AI 续写结果] 基于上文内容的续写：故事继续发展，新的情节展开..."
    elif operation_type == "summarize":
        return f"[AI 总结结果] 内容摘要：{text[:30]}... 的主要内容概括。"
    else:
        return f"[AI 处理结果] 处理后的文本内容"


@router.get("/{chapter_id}", response_model=schemas.Chapter)
def read_chapter(
    chapter_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取章节内容"""
    db_chapter = crud.get_chapter(db, chapter_id=chapter_id, user_id=current_user.id)
    if db_chapter is None:
        raise HTTPException(status_code=404, detail="章节不存在")
    return db_chapter


@router.post("/", response_model=schemas.Chapter)
def create_chapter(
    chapter: schemas.ChapterCreate,
    novel_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建新章节"""
    db_chapter = crud.create_chapter(
        db=db, chapter=chapter, novel_id=novel_id, user_id=current_user.id
    )
    if db_chapter is None:
        raise HTTPException(status_code=404, detail="小说不存在")
    return db_chapter


@router.put("/{chapter_id}", response_model=schemas.Chapter)
def update_chapter(
    chapter_id: int,
    chapter_update: schemas.ChapterUpdate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新章节内容"""
    db_chapter = crud.update_chapter(
        db, chapter_id=chapter_id, user_id=current_user.id, chapter_update=chapter_update
    )
    if db_chapter is None:
        raise HTTPException(status_code=404, detail="章节不存在")
    return db_chapter


@router.delete("/{chapter_id}", response_model=schemas.MessageResponse)
def delete_chapter(
    chapter_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除章节"""
    success = crud.delete_chapter(db, chapter_id=chapter_id, user_id=current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="章节不存在")
    return {"message": "章节删除成功", "success": True}


# AI 功能端点
@router.post("/ai/refine", response_model=schemas.AIResponse)
async def refine_text(
    request: schemas.AIRequest,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """AI 文本润色功能"""
    # 验证章节是否存在且属于当前用户
    chapter = crud.get_chapter(db, chapter_id=request.chapter_id, user_id=current_user.id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    try:
        # 调用 AI 服务（占位符）
        generated_text = await mock_ai_service(request.text, "refine")
        
        # 记录 AI 操作
        crud.create_ai_operation(
            db=db,
            operation_type="refine",
            original_text=request.text,
            generated_text=generated_text,
            chapter_id=request.chapter_id,
            user_id=current_user.id,
            prompt_used="文本润色提示词"
        )
        
        return schemas.AIResponse(
            generated_text=generated_text,
            operation_type="refine",
            original_text=request.text
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI 服务调用失败：{str(e)}")


@router.post("/ai/expand", response_model=schemas.AIResponse)
async def expand_text(
    request: schemas.AIRequest,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """AI 文本扩写功能"""
    # 验证章节是否存在且属于当前用户
    chapter = crud.get_chapter(db, chapter_id=request.chapter_id, user_id=current_user.id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    try:
        # 调用 AI 服务（占位符）
        generated_text = await mock_ai_service(request.text, "expand")
        
        # 记录 AI 操作
        crud.create_ai_operation(
            db=db,
            operation_type="expand",
            original_text=request.text,
            generated_text=generated_text,
            chapter_id=request.chapter_id,
            user_id=current_user.id,
            prompt_used="文本扩写提示词"
        )
        
        return schemas.AIResponse(
            generated_text=generated_text,
            operation_type="expand",
            original_text=request.text
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI 服务调用失败：{str(e)}")


@router.post("/ai/continue", response_model=schemas.AIResponse)
async def continue_chapter(
    request: schemas.AIRequest,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """AI 章节续写功能"""
    # 验证章节是否存在且属于当前用户
    chapter = crud.get_chapter(db, chapter_id=request.chapter_id, user_id=current_user.id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    try:
        # 获取章节的完整内容作为上下文
        context_text = chapter.content
        if request.text:
            context_text += "\n" + request.text
        
        # 调用 AI 服务（占位符）
        generated_text = await mock_ai_service(context_text, "continue")
        
        # 记录 AI 操作
        crud.create_ai_operation(
            db=db,
            operation_type="continue",
            original_text=context_text,
            generated_text=generated_text,
            chapter_id=request.chapter_id,
            user_id=current_user.id,
            prompt_used="章节续写提示词"
        )
        
        return schemas.AIResponse(
            generated_text=generated_text,
            operation_type="continue",
            original_text=context_text
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI 服务调用失败：{str(e)}")


@router.post("/ai/summarize", response_model=schemas.AIResponse)
async def summarize_text(
    request: schemas.AIRequest,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """AI 文本总结功能"""
    # 验证章节是否存在且属于当前用户
    chapter = crud.get_chapter(db, chapter_id=request.chapter_id, user_id=current_user.id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    try:
        # 调用 AI 服务（占位符）
        generated_text = await mock_ai_service(request.text, "summarize")
        
        # 记录 AI 操作
        crud.create_ai_operation(
            db=db,
            operation_type="summarize",
            original_text=request.text,
            generated_text=generated_text,
            chapter_id=request.chapter_id,
            user_id=current_user.id,
            prompt_used="文本总结提示词"
        )
        
        return schemas.AIResponse(
            generated_text=generated_text,
            operation_type="summarize",
            original_text=request.text
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI 服务调用失败：{str(e)}")


@router.get("/{chapter_id}/ai-history", response_model=List[schemas.AIOperation])
def get_ai_history(
    chapter_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取章节的 AI 操作历史"""
    # 验证章节是否存在且属于当前用户
    chapter = crud.get_chapter(db, chapter_id=chapter_id, user_id=current_user.id)
    if not chapter:
        raise HTTPException(status_code=404, detail="章节不存在")
    
    operations = crud.get_ai_operations(db, chapter_id=chapter_id, user_id=current_user.id)
    return operations
