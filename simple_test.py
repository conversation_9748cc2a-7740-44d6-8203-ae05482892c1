"""
简单测试 AI API 连接
"""
import httpx
import asyncio


async def test_ai_api():
    """测试 AI API 连接"""
    api_url = "https://poyo123456-geminilx.hf.space/v1/chat/completions"
    api_key = "AIzaSyApJq6FmjslzGxPjgdRZ_gyS8bXO5fJCt0"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "gemini-2.5-pro",
        "messages": [
            {
                "role": "user",
                "content": "请润色这段文字：这是一个测试。"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        print("🔗 正在测试 AI API 连接...")
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(api_url, headers=headers, json=payload)
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ API 调用成功！")
                print(f"📝 响应内容: {result}")
                
                if "choices" in result and len(result["choices"]) > 0:
                    generated_text = result["choices"][0]["message"]["content"]
                    print(f"🤖 AI 生成文本: {generated_text}")
                else:
                    print("⚠️ 响应格式异常")
            else:
                print(f"❌ API 调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_ai_api())
