<template>
  <div class="home-container">
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="app-title">
          <el-icon class="title-icon"><Edit /></el-icon>
          妙笔（InkMind）
        </h1>
        <p class="app-subtitle">AI 辅助小说写作平台</p>
        <p class="app-description">
          让 AI 成为您的创作伙伴，提供智能润色、扩写、续写等功能，<br>
          助您创作出更精彩的小说作品。
        </p>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            @click="goToEditor"
          >
            开始创作
          </el-button>
          <el-button
            size="large"
            @click="goToLogin"
          >
            登录 / 注册
          </el-button>
        </div>
      </div>
    </div>

    <div class="features-section">
      <h2>核心功能</h2>
      <div class="features-grid">
        <div class="feature-card">
          <el-icon class="feature-icon"><Magic /></el-icon>
          <h3>AI 智能润色</h3>
          <p>选中文本，一键润色，让您的文字更加优美流畅</p>
        </div>

        <div class="feature-card">
          <el-icon class="feature-icon"><DocumentAdd /></el-icon>
          <h3>内容扩写</h3>
          <p>AI 帮您扩展情节，丰富细节描述，让故事更加生动</p>
        </div>

        <div class="feature-card">
          <el-icon class="feature-icon"><Promotion /></el-icon>
          <h3>章节续写</h3>
          <p>基于上文内容，AI 智能续写，激发您的创作灵感</p>
        </div>

        <div class="feature-card">
          <el-icon class="feature-icon"><FolderOpened /></el-icon>
          <h3>小说管理</h3>
          <p>清晰的章节结构，便捷的文件导入，让创作更有条理</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Edit, Magic, DocumentAdd, Promotion, FolderOpened } from '@element-plus/icons-vue'

const router = useRouter()

// 方法
const goToEditor = () => {
  router.push('/editor')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: var(--ink-bg-primary);
  color: var(--ink-text-primary);
}

.welcome-section {
  padding: 80px 20px;
  text-align: center;
  background: linear-gradient(135deg, var(--ink-bg-primary) 0%, var(--ink-bg-secondary) 100%);
}

.welcome-content {
  max-width: 800px;
  margin: 0 auto;
}

.app-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: var(--ink-text-primary);
}

.title-icon {
  font-size: 48px;
  color: var(--ink-accent);
}

.app-subtitle {
  font-size: 24px;
  color: var(--ink-accent);
  margin-bottom: 24px;
  font-weight: 500;
}

.app-description {
  font-size: 18px;
  color: var(--ink-text-secondary);
  line-height: 1.6;
  margin-bottom: 40px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.features-section {
  padding: 80px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.features-section h2 {
  text-align: center;
  font-size: 32px;
  margin-bottom: 48px;
  color: var(--ink-text-primary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.feature-card {
  padding: 32px 24px;
  background: var(--ink-bg-secondary);
  border-radius: 12px;
  text-align: center;
  border: 1px solid var(--ink-border);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  border-color: var(--ink-accent);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.1);
}

.feature-icon {
  font-size: 48px;
  color: var(--ink-accent);
  margin-bottom: 16px;
}

.feature-card h3 {
  font-size: 20px;
  margin-bottom: 12px;
  color: var(--ink-text-primary);
}

.feature-card p {
  color: var(--ink-text-secondary);
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-title {
    font-size: 36px;
  }

  .app-subtitle {
    font-size: 20px;
  }

  .app-description {
    font-size: 16px;
  }

  .features-section h2 {
    font-size: 28px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
