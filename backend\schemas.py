"""
Pydantic 数据模式定义
用于 API 请求和响应的数据验证
"""
from __future__ import annotations
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime


# 用户相关模式
class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr


class UserCreate(UserBase):
    """用户创建模式"""
    password: str


class UserUpdate(BaseModel):
    """用户更新模式"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None


class User(UserBase):
    """用户响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# 小说相关模式
class NovelBase(BaseModel):
    """小说基础模式"""
    title: str
    description: Optional[str] = None
    cover_image: Optional[str] = None
    status: Optional[str] = "draft"


class NovelCreate(NovelBase):
    """小说创建模式"""
    pass


class NovelUpdate(BaseModel):
    """小说更新模式"""
    title: Optional[str] = None
    description: Optional[str] = None
    cover_image: Optional[str] = None
    status: Optional[str] = None


# 章节相关模式
class ChapterBase(BaseModel):
    """章节基础模式"""
    title: str
    content: str
    chapter_number: int


class ChapterCreate(ChapterBase):
    """章节创建模式"""
    pass


class ChapterUpdate(BaseModel):
    """章节更新模式"""
    title: Optional[str] = None
    content: Optional[str] = None
    chapter_number: Optional[int] = None


class Chapter(ChapterBase):
    """章节响应模式"""
    id: int
    word_count: int
    novel_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Novel(NovelBase):
    """小说响应模式"""
    id: int
    word_count: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class NovelWithChapters(Novel):
    """包含章节的小说模式"""
    chapters: List[Chapter] = []


# AI 功能相关模式
class AIRequest(BaseModel):
    """AI 请求模式"""
    text: str
    operation_type: str  # refine, expand, continue, summarize
    chapter_id: int


class AIResponse(BaseModel):
    """AI 响应模式"""
    generated_text: str
    operation_type: str
    original_text: str


class AIOperation(BaseModel):
    """AI 操作记录模式"""
    id: int
    operation_type: str
    original_text: str
    generated_text: str
    prompt_used: Optional[str] = None
    user_id: int
    chapter_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


# 认证相关模式
class Token(BaseModel):
    """令牌模式"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None


class LoginRequest(BaseModel):
    """登录请求模式"""
    username: str
    password: str


# 文件导入相关模式
class FileImportRequest(BaseModel):
    """文件导入请求模式"""
    filename: str
    content: str
    novel_id: int


# 通用响应模式
class MessageResponse(BaseModel):
    """消息响应模式"""
    message: str
    success: bool = True
